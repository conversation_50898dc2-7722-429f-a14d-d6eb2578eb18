import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:likewallet/controller/otherController/logoStoreController.dart';
import 'package:likewallet/controller/transferController/transferController.dart';
import 'package:likewallet/model/tansferModel/vendingModel.dart';
import 'package:likewallet/view/transferPoint/confirmTransection.dart';
import 'package:likewallet/view/transferPoint/mainBankPage.dart';
import 'package:scan/scan.dart';

class ScanController extends GetxController {
  // Controllers
  late LogoStoreController logoStoreCtrl;
  late TransferController transferCtrl;

  RxString whichPage = ''.obs; // Track which page is active

  // Variables
  final ImagePicker _picker = ImagePicker();
  RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();

    // Initialize controllers
    try {
      logoStoreCtrl = Get.find<LogoStoreController>();
      transferCtrl = Get.find<TransferController>();
    } catch (e) {
      print('Error initializing controllers: $e');
      // Create controllers if they don't exist
      if (!Get.isRegistered<LogoStoreController>()) {
        logoStoreCtrl = Get.put(LogoStoreController());
      }
      if (!Get.isRegistered<TransferController>()) {
        transferCtrl = Get.put(TransferController());
      }
    }
  }

  // Process QR code data
  Future<void> processQRData(String? qrData) async {
    if (qrData == null || qrData.isEmpty) {
      Get.snackbar(
        'Error',
        'Invalid QR code',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      // Check if it's a wallet address
      if (qrData.startsWith('0x')) {
        // Handle wallet address
        transferCtrl.addressText.text = qrData;
        Get.back(); // Return to transfer page
        return;
      }

      // Check if it's a store
      if (qrData.startsWith('store')) {
        await _processStoreQR(qrData);
        return;
      }

      // Try to parse as vending
      await _processVendingQR(qrData);

    } catch (e) {
      print('Error processing QR data: $e');
      Get.snackbar(
        'Error',
        'Failed to process QR code: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Process store QR code
  Future<void> _processStoreQR(String qrData) async {
    print("_processStoreQR");
    String storeAddress = qrData.replaceAll('store', '');

    // Find store in the list
    var storeFound = false;
    for (var store in logoStoreCtrl.list) {
      if (store.address == storeAddress) {
        storeFound = true;

        // Set transfer details
        transferCtrl.addressText.text = store.address ?? '';
        transferCtrl.titleName.value = store.title ?? '';


        break;
      }
    }

    if (!storeFound) {
      Get.snackbar(
        'Error',
        'Store not found',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Process vending QR code
  Future<void> _processVendingQR(String qrData) async {
    try {
      // Parse JSON data
      Vending vending = Vending.fromJson(jsonDecode(qrData));

      // Check if it's a vending transfer
      if (vending.function == 'transfer_vending') {
        // Set vending data in transfer controller
        transferCtrl.setVendingData(vending);

        // set is vending to true
        transferCtrl.allowVending();

        print("haha I GOT YOU!!");
        // Navigate to confirmation page
        Get.to(() => const ConfirmTransection());
      } else {
        Get.snackbar(
          'Error',
          'Unsupported vending function',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      print('Error parsing vending data: $e');
      Get.snackbar(
        'Error',
        'Invalid vending QR code',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Pick image from gallery and scan
  Future<void> scanFromGallery() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        isLoading.value = true;

        // Scan QR from image
        String? result = await Scan.parse(pickedFile.path);

        isLoading.value = false;

        // Process the result
        await processQRData(result);
      }
    } catch (e) {
      isLoading.value = false;
      print('Error scanning from gallery: $e');
      Get.snackbar(
        'Error',
        'Failed to scan image: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void setPage(String page) {
    whichPage.value = page;
  }
}
