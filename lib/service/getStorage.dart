import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/controller/setting_controller/setting_controller.dart';
import 'package:likewallet/view/login/index.dart';

// คลาสสำหรับจัดการ keys ของ GetStorage
class StorageKeys {
  static const String userId = 'user_id';
  static const String phoneNumber = 'phone_number';
  static const String passcode = 'passcode';
  static const String isLoggedIn = 'is_logged_in';
  static const String language = 'language';
  static const String agreementPolicy = 'agreementPolicy';
  static const String agreementTermsAndCondition = 'agreementTermsAndCondition';
  static const String addressETH = 'addressETH';
  static const String didSetupWallet = 'didSetupWallet';
  static const String mnemonic = 'mnemonic';
  static const String tokenLine = 'tokenLine';
  static const String keyEncrypt = 'keyEncrypt';
  static const String ivEncrypt = 'ivEncrypt';
  static const String privateKey = 'privateKey';
  static const String secret = 'secret';
  static const String selectedCurrency = 'selectedCurrency';
  static const String currency = 'currency';
  static const String currencyRatio = 'currency_ratio';
  static const String kycActive = 'kycActive';
  static const String login = 'login';
  static const String tierLevel = 'tierLevel';
  static const String actionSyncContact = 'actionSyncContact';
  static const String syncContact = 'syncContact';
  static const String saveSlip = 'saveSlip';
  static const String availableSymbols = 'available_symbols';
  static const String tokenNotify = 'tokenNotify';
  static const String biometricEnabled = 'biometric_enabled';
  static const String activeTouchID = 'activeTouchID';
  static const String saveSlipAllow = 'saveSlipAllow';
  static const String notifyAllow = 'notifyAllow';
}

// Static class สำหรับจัดการ GetStorage
class Storage {
  static final GetStorage _storage = GetStorage();

  // บันทึกข้อมูล
  static Future<void> save(String key, dynamic value) => _storage.write(key, value);

  // อ่านข้อมูล
  static T? get<T>(String key) => _storage.read<T>(key);

  // ลบข้อมูล
  static void remove(String key) => _storage.remove(key);

  // ล้างข้อมูลทั้งหมด
  static void clear() => _storage.erase();

  String getCurrentCurrencyKey() {
    final storage = GetStorage();
    return storage.read('selected_currency') ?? 'thb';
  }

  // ฟังก์ชัน logout
  static Future<void> logout() async {
    try {
      print('Starting logout process...');

      // ล้างข้อมูลทั้งหมดใน Storage
      await _storage.erase();

      // ตรวจสอบว่าข้อมูลถูกล้างจริงหรือไม่
      await Future.delayed(const Duration(milliseconds: 100));
      final passcodeCheck = _storage.read(StorageKeys.passcode);
      final loginCheck = _storage.read(StorageKeys.login);
      print('After erase - Passcode: $passcodeCheck, Login: $loginCheck');

      // ล้างข้อมูลเฉพาะ keys ที่สำคัญเพิ่มเติม (เผื่อ erase ไม่สมบูรณ์)
      final criticalKeys = [
        StorageKeys.passcode,
        StorageKeys.login,
        StorageKeys.isLoggedIn,
        StorageKeys.userId,
        StorageKeys.phoneNumber,
        StorageKeys.addressETH,
        StorageKeys.privateKey,
        StorageKeys.mnemonic,
        StorageKeys.secret,
        StorageKeys.tokenLine,
        StorageKeys.keyEncrypt,
        StorageKeys.ivEncrypt,
      ];

      for (String key in criticalKeys) {
        _storage.remove(key);
      }

      print('Critical keys cleared individually');

      // ออกจาก Firebase Authentication
      try {
        await FirebaseAuth.instance.signOut();
        print('Firebase user signed out');
      } catch (firebaseError) {
        print('Firebase signOut error: $firebaseError');
        // ไม่ให้ error นี้หยุดการ logout
      }

      // ลบ controllers ที่เฉพาะเจาะจงแทนการใช้ Get.reset() ที่อาจทำลาย navigation context
      try {
        // ลบ controllers ที่เกี่ยวข้องกับ user session
        if (Get.isRegistered<ProfileController>()) {
          Get.delete<ProfileController>();
        }
        // เพิ่ม controllers อื่นๆ ที่ต้องการลบตามความจำเป็น
        print('Specific controllers removed');
      } catch (controllerError) {
        print('Error removing controllers: $controllerError');
      }

      // นำทางไปยังหน้า Login และล้าง Navigation Stack
      Get.offAll(() => const IndexLike());
      print('Navigated to login screen');

      // รอสักครู่เพื่อให้การนำทางเสร็จสิ้น
      await Future.delayed(const Duration(milliseconds: 200));

      // ตรวจสอบและใส่ SettingController ใหม่ถ้าจำเป็น
      if (!Get.isRegistered<SettingController>()) {
        Get.put(SettingController(), permanent: true);
        print('SettingController reinitialized');
      }

    } catch (e, stacktrace) {
      // จัดการ error และแสดง Stacktrace เพิ่มเพื่อ debug ง่ายขึ้น
      print('Logout error: $e');
      print('Stacktrace: $stacktrace');

      // ถ้า logout ปกติไม่ได้ ให้ลองบังคับล้างและ restart app
      try {
        await _storage.erase();
        try {
          await FirebaseAuth.instance.signOut();
        } catch (firebaseError) {
          print('Fallback Firebase signOut error: $firebaseError');
        }
        Get.offAll(() => const IndexLike());
      } catch (fallbackError) {
        print('Fallback logout also failed: $fallbackError');
      }

      rethrow;
    }
  }

}