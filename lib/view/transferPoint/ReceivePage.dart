import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:qr_flutter/qr_flutter.dart';

import 'dart:ui' as ui;
import 'dart:typed_data';
import 'dart:io';

import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';


class ReceiveLIKEPage extends StatelessWidget {
  final String walletAddress = Storage.get(StorageKeys.addressETH);

  final GlobalKey QRKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 300.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 70.w),
          child: Text(
            walletAddress,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 14.sp,
              color: Colors.grey,
            ),
          ),
        ),
        SizedBox(height: 24.h),
        RepaintBoundary(
          key: QR<PERSON><PERSON>,
          child: Container(
            padding: EdgeInsets.all(8.sp),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: QrImageView(
              data: walletAddress,
              size: 230.h,
              embeddedImage:
              AssetImage(LikeWalletImage.qr_likepoint),
              embeddedImageStyle: QrEmbeddedImageStyle(
                size: Size(
                  55.h,
                  55.h,
                ),
              ),
              errorCorrectionLevel: QrErrorCorrectLevel.Q,
              gapless: false,
              version: 9,
            ),
          ),
        ),
        SizedBox(height: 36.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            InkWell(
              onTap: () async {
                saveQRImage();
              },
                child: _iconAction('assets/image/Save_receipt.png', 'button_qr_save'.tr),
            ),
            InkWell(
              onTap: () async {
                await Clipboard.setData(ClipboardData(text: walletAddress));
                Get.snackbar(
                  'Success',
                  'Address copied to clipboard!',
                  snackPosition: SnackPosition.BOTTOM,
                  colorText: Colors.black,
                  duration: Duration(seconds: 1),
                );
              },
                child: _iconAction('assets/image/receive/copy_address.png', 'bankingreceive_button_copy'.tr),
            ),
            _iconAction('assets/image/Share_receipt.png', 'bankingreceive_button_address'.tr),
          ],
        ),
      ],
    );
  }

  Widget _iconAction(String assetPath, String label) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: Colors.white,
          child: Image.asset(
            assetPath,
            fit: BoxFit.contain,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontFamily: 'Proxima Nova',
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade700,
          ),
        ),
      ],
    );
  }

  Future<void> saveQRImage() async {
    try {
      final boundary = QRKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        print("❌ QRKey not found or not rendered yet.");
        return;
      }

      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List pngBytes = byteData!.buffer.asUint8List();

      // เลือก path สำหรับจัดเก็บไฟล์
      final directory = await getApplicationDocumentsDirectory(); // หรือ getDownloadsDirectory() บน desktop
      final filePath = '${directory.path}/qr_code_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File(filePath);

      await file.writeAsBytes(pngBytes);
      print("✅ QR image saved at: $filePath");

      Get.snackbar(
        'Success',
        'QR code image saved successfully!',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

    } catch (e) {
      print("❌ Failed to save QR image: $e");
      Get.snackbar(
        'Error',
        'Failed to save QR code image: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

}
